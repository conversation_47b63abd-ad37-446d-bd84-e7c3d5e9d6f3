# devbase: ensure=always
# https://github.com/direnv/direnv

realpath() {
  [[ $1 = /* ]] && echo "$1" || echo "$PWD/${1#./}"
}

path_add() {
  if [[ ! $PATH = *"$1"* ]]; then
    export PATH="$1:$PATH"
  fi
}

export __project_path=$(pwd)
export __uid=$(id -u)
export __gid=$(id -g)
export __docker_sock_path=/var/run/docker.sock
export __docker_gid=$(\ls -n $__docker_sock_path | awk '{print $4}')
export __dev_port=44000
export COMPOSE_PATH_SEPARATOR=:
export COMPOSE_FILE=./build/docker-compose.yml
export FOLDER_NAME=${__project_path##*/}
export COMPOSE_PROJECT_NAME=$(echo $FOLDER_NAME | tr -d '-')
export NVIM_LISTEN_ADDRESS=/tmp/nvim/$COMPOSE_PROJECT_NAME
export APP_VERSION=dev
if [[ $(uname) == "Darwin" ]]; then
  export EZNGINX_IMAGE_TAG=4-arm
fi

if [[ -f ./.envrc.custom ]]; then
  source ./.envrc.custom
  # e.g. use the production compose setup to test production build
  #   export COMPOSE_FILE=./build/docker-compose.yml:./build/docker-compose.prod.yml
fi

if which ip 1>/dev/null 2>/dev/null; then # ubuntu
  default_nic=$(ip r | grep '^default' | sed -e 's/.* dev //' | awk '{print $1}')
  export __host_ip=$(ip r | grep "dev $default_nic" | grep src | head -n1 | sed -e 's/.* src //' | awk '{print $1}')
else
  default_nic=$(route -n get default | grep interface | awk '{print $2}')
  export __host_ip=$(ifconfig en0 | grep "inet " | awk '{print $2}')
fi

path_add "$__project_path/.devbase/bin"

mkdir -p /tmp/nvim # for remote access
mkdir -p node_modules src/assets/static/img
mkdir -p public/assets/static/img
if [[ ! -d dist ]]; then
  mkdir -p dist
fi
