coverage
dist
dev-dist
dist-ssr
node_modules
*.log
.direnv
.envrc.custom
.DS_Store
.babel-error
.eslintcache
*.gz
.skip-starting-bg-jobs

.yarn/*
!.yarn/cache
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# PWA specific
sw.js.map
workbox-*.js.map
registerSW.js.map

# PWA build artifacts (generated by vite-plugin-pwa)
dist/sw.js
dist/workbox-*.js
dist/registerSW.js
dist/manifest.webmanifest

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Cache directories
.cache/
.parcel-cache/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
Thumbs.db
._*
.Spotlight-V100
.Trashes
ehthumbs.db

# Docker development volumes
.docker-volumes/

# Test coverage
coverage/

# Large background images (keep icons)
public/assets/static/img/bg*.jpg
