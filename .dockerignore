# Version control
.git/
.gitignore

# Build outputs (exclude from dev builds, include in prod builds)
dist/
dev-dist/
dist-ssr/
coverage/

# Dependencies (will be installed in container)
node_modules/

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
*.log

# Test files
test/
tests/
__tests__/
*.test.js
*.test.jsx
*.spec.js
*.spec.jsx

# Development files
.dockerignore
.editorconfig
README.md
*~
.devbase/

# Environment files (use build args instead)
.env*
.envrc*

# Cache directories
.cache/
.parcel-cache/
.eslintcache

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Editor directories and files
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
Thumbs.db
._*
.Spotlight-V100
.Trashes
ehthumbs.db

# PWA specific development files
sw.js.map
workbox-*.js.map
registerSW.js.map

# Large source files not needed in production
public/assets/static/img/bg*.jpg
