services:
  web:
    image: appcara.azurecr.io/fms-pwa:${APP_VERSION:-latest}
    restart: always
    ports:
      - "${NGINX_PORT:-80}:80"
    environment:
      CONFIG: |
        workers: 2
        servers:
        - port: 80
          name: _
          max_body_size: 100M
          locations:
          # API proxy to FMS server - strip /api prefix
          - name: /api/
            proxy_pass:
              to: ${FMS_SERVER_URL:-https://fms-dev.com}/
              headers:
                Host: ${FMS_SERVER_HOST:-fms-dev.com}
          # Serve PWA static files
          - name: /
            root: /usr/share/nginx/html
            try_files: $$uri $$uri/ /index.html
