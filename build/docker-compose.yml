networks:
  default:
    external: true
    name: fms_default

services:
  pwa-app:
    extends:
      file: base.yml
      service: appservice
    build:
      context: .
      target: development
      args:
        - appuser_uid=${__uid}
    image: fms-pwa/dev
    volumes:
      - /tmp/nvim:/tmp/nvim
      - ${__project_path}:/app
      - ${__project_path}/node_modules:/app/node_modules
    extra_hosts:
      - "desktop:${__host_ip}"
    command: npm run dev -- --host 0.0.0.0 --port 5173

  # for testing frontend in local
  # ip mapping will be added to /etc/hosts automatically during reboot-dev
  web:
    image: appcara.azurecr.io/eznginx:${EZNGINX_IMAGE_TAG:-4}
    restart: always
    depends_on:
      - pwa-app
    ports:
      - "3000:80"   # HTTP for localhost (service worker compatible)
      - "3443:443"  # HTTPS for custom domain (service worker compatible)
    volumes:
      - ${__project_path}/.devbase/ssl:/ssl
    environment:
      NGINX_UID: ${__uid}
      CONFIG: |
        workers: 2
        servers:
        - port: 443
          name: fmspwa-dev.com
          max_body_size: 10M
          ssl:
            cert: /ssl/appcara.crt
            key: /ssl/appcara.key
          locations:
          - name: /
            proxy_pass:
              to: http://pwa-app:5173
              websocket: true
        - port: 80
          name: fmspwa-dev.com
          locations:
          - name: /
            return: 301 https://$$host:3443$$request_uri
        - port: 80
          name: localhost
          max_body_size: 10M
          locations:
          - name: /
            proxy_pass:
              to: http://pwa-app:5173
              websocket: true
            headers:
              - "Strict-Transport-Security: max-age=0"

  dns:
    restart: always
    image: strm/dnsmasq
    init: true
    volumes:
      - ${__project_path}/build:/build
    cap_add:
      - NET_ADMIN
    depends_on:
      - web
    entrypoint: /build/dnsmasq_setup.sh
