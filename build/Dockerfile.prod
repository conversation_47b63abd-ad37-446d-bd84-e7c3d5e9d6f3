# Multi-stage build for FMS PWA
FROM node:20.17-bullseye AS builder

WORKDIR /app

# Set npm configuration for better network handling (before package copy for caching)
RUN npm config set registry https://registry.npmjs.org/ && \
    npm config set fetch-timeout 300000

# Copy package files
COPY package*.json ./

# Remove problematic sharp-dependent packages from package.json
RUN sed -i '/@vite-pwa\/assets-generator/d' package.json && \
    sed -i '/sharp/d' package.json && \
    sed -i '/sharp-ico/d' package.json

# Install dependencies (this layer will be cached if package.json doesn't change)
RUN npm ci || npm install

# Copy source code (separate layer for better caching)
COPY . .

# Build the application (without PWA asset generation)
RUN npm run build

# Production stage
FROM appcara.azurecr.io/eznginx:4

# Copy built assets from builder stage
COPY --from=builder /app/dist/ /usr/share/nginx/html/

# Expose port
EXPOSE 80
