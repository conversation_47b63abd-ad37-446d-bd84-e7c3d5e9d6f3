FROM node:20.17-bullseye AS development

WORKDIR /app

RUN npm install -g --force yarn
RUN YARN_IGNORE_NODE=1 yarn set version 4.5.0

# psql needed for resetdb
RUN \
  apt-get update && \
  apt-get install -y zip ca-certificates curl build-essential iputils-ping telnet iproute2 python python3-pip && \
  apt-get install -y postgresql-client gosu && \
  apt-get install -y redis-tools &&\
  pip3 install neovim-remote && \
  rm -rf /var/lib/apt/lists/*

ARG appuser_uid

RUN mkdir /app/node_modules && chown $appuser_uid /app/node_modules

# OTF font to fix sub-pixel hinting issue of rendering PDF with phantomJS
COPY fonts/* /usr/share/fonts/
RUN set -x && \
  chmod 644 /usr/share/fonts/*.otf && \
  fc-cache -fv
RUN \
  curl https://cdn.pointup.co/msjh.ttf > /usr/share/fonts/truetype/msjh.ttf && \
  chmod 644 /usr/share/fonts/truetype/msjh.ttf && \
  fc-cache -fv

# dos2unix for converting ace output to linux text format
# and dependencies for puppeteer (pdf generation)
RUN \
  apt-get update && \
  apt-get install -y dos2unix \
    # for puppeteer (receipt pdf generation)
    libatk-bridge2.0-0 libdrm2 libxkbcommon0 libgbm1 libasound2 \
    libnss3-dev libgdk-pixbuf2.0-dev libgtk-3-dev libxss-dev &&\
  rm -rf /var/lib/apt/lists/*

ENV ASSETS_CONTEXT /assets
