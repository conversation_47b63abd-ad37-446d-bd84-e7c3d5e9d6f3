FROM node:20.17-bullseye AS development

WORKDIR /app

# Install essential development tools for PWA
RUN \
  apt-get update && \
  apt-get install -y ca-certificates curl build-essential python3-pip && \
  pip3 install neovim-remote && \
  rm -rf /var/lib/apt/lists/*

ARG appuser_uid

RUN mkdir /app/node_modules && chown $appuser_uid /app/node_modules

# OTF fonts for consistent rendering
COPY fonts/* /usr/share/fonts/
RUN set -x && \
  chmod 644 /usr/share/fonts/*.otf && \
  fc-cache -fv
RUN \
  curl https://cdn.pointup.co/msjh.ttf > /usr/share/fonts/truetype/msjh.ttf && \
  chmod 644 /usr/share/fonts/truetype/msjh.ttf && \
  fc-cache -fv

# PWA-specific environment
ENV NODE_ENV=development
ENV VITE_PWA_ENABLED=true
