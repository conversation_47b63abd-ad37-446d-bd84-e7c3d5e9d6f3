# FMS PWA - Facility Management System Progressive Web App

A modern, offline-capable Progressive Web App for the Facility Management System, built with React 19, Vite 6, and Material-UI v7. This PWA enables field engineers to work offline and synchronize data when connectivity is restored.

## Features

### Core Functionality
- **Offline-First Architecture**: Complete offline functionality with encrypted local storage
- **OTP-Based Authentication**: Secure service access using supervisor-generated OTPs
- **Three-Step Workflow**: Initialize Service → Complete System Forms → Submit & Signoff
- **Digital Signatures**: Customer signature capture with print name validation
- **File Management**: Upload and manage service-related documents and attachments
- **Parts Tracking**: Record and manage replaced parts during service operations
- **Data Synchronization**: Automatic sync when online connectivity is restored

### Progressive Web App Features
- **Service Worker**: Automatic caching and offline functionality with Workbox
- **App Installation**: Install as native app on mobile devices and desktops
- **Background Sync**: Queue operations for when connectivity returns
- **Responsive Design**: Optimized for mobile, tablet, and desktop interfaces
- **Push Notifications**: Service updates and sync notifications (when supported)

## Architecture

### Technology Stack
- **Frontend**: React 19 with hooks and context API
- **Build Tool**: Vite 6 with hot module replacement and optimized builds
- **UI Framework**: Material-UI (MUI) v7 with custom theme
- **PWA**: Vite PWA Plugin with Workbox for service worker generation
- **State Management**: React Context + Custom hooks for service state
- **Storage**: LocalStorage (encrypted) + IndexedDB for offline data
- **Deployment**: Docker with nginx for production serving

### Project Structure
```
src/
├── components/              # React components
│   ├── OfflineServiceManager.jsx    # Main service workflow orchestrator
│   ├── FMSSystemsRenderer.jsx       # System table and form management
│   ├── SignaturePad.jsx             # Digital signature capture
│   ├── AppContent.jsx               # Main app wrapper
│   └── WiFiStatusIndicator.jsx      # Network status display
├── services/               # API and data services
│   ├── offlineServiceAPI.js         # FMS server communication
│   ├── formDataService.js           # Form data management
│   ├── uploadService.js             # File and data uploads
│   ├── indexedDBService.js          # Large file storage
│   └── fmsTemplateService.js        # Form template fetching
├── hooks/                  # Custom React hooks
│   ├── useNetworkStatus.js          # Online/offline detection
│   ├── useServiceState.js           # Service state management
│   └── useFormEngine.js             # Form rendering engine
├── contexts/               # React context providers
│   └── AppProvider.jsx              # Combined app state provider
├── core/                   # FMS integration layer
│   ├── FMSEnvironment.js            # FMS environment setup
│   ├── FormRenderingEngine.js       # FMS form compatibility
│   └── RenderRegistry.js            # Component registration
├── frontend/               # FMS frontend integration
│   ├── fcsr.js                      # Form conversion and rendering
│   ├── fms-frontend.js              # FMS frontend initialization
│   └── signature.js                 # Signature functionality
├── utils/                  # Utility functions
│   ├── localStorageManager.js       # Encrypted local storage
│   ├── errorHandler.js              # Error handling and logging
│   └── validation.js                # Data validation utilities
└── config/                 # Configuration
    └── api.js                       # API endpoint configuration
```

## Installation & Setup

### Prerequisites
- Node.js 20.17+
- Docker & Docker Compose
- direnv (for environment management)

### Development Setup

1. **Clone and Initialize**
   ```bash
   git clone <repository-url>
   cd fms-pwa
   
   # Install direnv and initialize environment
   sudo apt install direnv
   direnv allow .
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Start Development Environment**
   ```bash
   # Using Docker (recommended)
   .devbase/bin/reboot-dev
   
   # Or locally
   npm run dev
   ```

4. **Access the Application**
   - Local: http://localhost:5173
   - Docker: http://localhost:3000 (HTTP) or https://fmspwa-dev.com:3443 (HTTPS)

### Production Deployment

1. **Build Production Image**
   ```bash
   # Using automated build script (recommended)
   .devbase/bin/build.prod
   
   # Or manual build
   docker build -f build/Dockerfile.prod -t appcara.azurecr.io/fms-pwa:latest .
   ```

2. **Deploy with Docker Compose**
   ```bash
   # Set environment variables
   export APP_VERSION=latest
   export FMS_SERVER_URL=https://your-fms-server.com
   export NGINX_PORT=80
   
   # Deploy
   docker compose -f build/docker-compose.prod.yml up -d
   ```

3. **Production Features**
   - Multi-stage Docker build for optimized image size
   - nginx serving with API proxy to FMS server
   - Automatic HTTPS redirect and security headers
   - Environment-based configuration

### Environment Variables
```bash
# API Configuration
FMS_SERVER_URL=https://fms-dev.com
VITE_APP_ENV=development

# PWA Configuration
VITE_APP_NAME="FMS PWA"
VITE_APP_VERSION=dev
VITE_PWA_ENABLED=true
```

## Usage

### Service Workflow

1. **Initialize Service** (Step 1)
   - Enter service number and OTP provided by supervisor
   - System downloads and caches service metadata
   - Form templates are fetched and stored locally
   - Service data is encrypted and stored for offline access

2. **Complete System Forms** (Step 2)
   - View systems table with status indicators
   - Select systems to open form dialogs
   - Complete forms using FMS-compatible rendering
   - Data is automatically saved locally with encryption
   - Forms can be completed and modified offline

3. **Submit & Signoff** (Step 3)
   - Review pending submissions and upload statistics
   - Capture customer signature with print name requirement
   - Upload forms, attachments, and signature when online
   - Complete service submission and cleanup local data

### Offline Capabilities
- **Form Completion**: All forms can be completed offline
- **Data Persistence**: Encrypted local storage with OTP-based encryption
- **File Attachments**: Large files stored in IndexedDB
- **Queue Management**: Operations queued for online synchronization
- **Signature Capture**: Digital signatures saved locally until upload

### Data Security
- **AES Encryption**: All local data encrypted using OTP as key
- **Secure Storage**: Sensitive data never stored in plain text
- **Auto-Cleanup**: Data automatically cleared after successful submission
- **Session Management**: OTP-based authentication with timeout

### Network Security
- **HTTPS Only**: All API communications over HTTPS
- **CORS Configuration**: Proper cross-origin resource sharing
- **Certificate Validation**: SSL certificate verification
- **Proxy Configuration**: API calls proxied to avoid CORS issues

## Testing

### Development Testing
```bash
# Run linting
npm run lint

# Test PWA functionality
# Access via HTTPS for full PWA features
https://fmspwa-dev.com:3443
```

### Service Worker Testing
- Install app to home screen
- Test offline functionality
- Verify background sync
- Check push notifications

## Monitoring & Debugging

### Logging
- **Console Logging**: Detailed operation logs
- **Error Tracking**: Centralized error handling
- **Network Monitoring**: API call success/failure tracking
- **Performance Metrics**: Load times and operation duration

### Debug Tools
- **Browser DevTools**: PWA and Service Worker inspection
- **Network Tab**: API call monitoring
- **Application Tab**: Storage and cache inspection
- **Lighthouse**: PWA compliance and performance auditing

## Contributing

### Development Guidelines
- Follow React best practices and hooks patterns
- Use Material-UI components for consistency
- Implement proper error handling and loading states
- Ensure offline functionality for all features
- Write comprehensive JSDoc comments

### Code Style
- ESLint configuration for code quality
- Prettier for code formatting
- Consistent naming conventions
- Modular component architecture

## API Integration

### Endpoint Configuration
The PWA integrates with FMS server APIs:

```javascript
// API endpoints (all under root domain)
GET  /uid/get-service-metadata/{serviceNumber}  // Service initialization
POST /uid/submit-service-forms                  // Form submissions
POST /uid/submit-service-signature             // Signature upload
POST /uid/upload-service-attachments           // File uploads
GET  /uid/get-mixins/{templateId}              // Form templates
```

### Authentication
- **OTP-Based**: Each service uses unique OTP from supervisor
- **Session Management**: OTP stored in memory, cleared on reset
- **Credential Validation**: Service number + OTP required for all operations

### Data Flow
1. **Initialization**: Download service metadata and form templates
2. **Offline Work**: Forms and data stored locally with encryption
3. **Synchronization**: Bulk upload when connectivity restored
4. **Completion**: Signature upload finalizes service

## Configuration Files

### Core Configuration
- `vite.config.js` - Vite build configuration with PWA plugin
- `package.json` - Dependencies and build scripts
- `manifest.webmanifest` - PWA manifest (auto-generated)
- `pwa-assets.config.js` - PWA icon and asset configuration

### Docker Configuration
- `build/Dockerfile` - Development container (Node.js 20.17 + essential tools)
- `build/Dockerfile.prod` - Production build configuration
- `build/docker-compose.yml` - Development environment with hot reload
- `build/docker-compose.prod.yml` - Production deployment with API proxy
- `build/base.yml` - Shared Docker service configuration
- `.dockerignore` - Optimized build context exclusions

### Environment Setup
- `.envrc` - direnv environment variables for Docker development
- `.devbase/bin/` - Development scripts and utilities:
  - `reboot-dev` - Start/restart development environment
  - `build.prod` - Production build and deployment script
  - `cleanup-all-images` - Docker image cleanup utility

## Docker Configuration

### Architecture Overview
The FMS PWA uses a modern Docker setup with separate development and production configurations, optimized for performance and maintainability.

### Development Environment
```bash
# Start development environment
.devbase/bin/reboot-dev

# Or manually
docker compose build
docker compose up -d
```

**Features:**
- **Base**: Node.js 20.17 with essential development tools only
- **Hot Reload**: Vite dev server with WebSocket support on port 5173
- **SSL/HTTPS**: Custom domain (fmspwa-dev.com) with self-signed certificates
- **API Proxy**: nginx handles CORS and proxies `/api/*` to FMS server
- **Volume Mounting**: Live code editing with instant updates
- **Service Discovery**: Automatic DNS resolution for custom domains

### Production Deployment
```bash
# Automated build and deploy
.devbase/bin/build.prod

# Manual production build
docker build -f build/Dockerfile.prod -t appcara.azurecr.io/fms-pwa:latest .

# Deploy with environment variables
APP_VERSION=v1.0.0 \
FMS_SERVER_URL=https://your-fms-server.com \
NGINX_PORT=80 \
docker compose -f build/docker-compose.prod.yml up -d
```

**Production Features:**
- **Multi-stage Build**: Optimized builder stage + minimal nginx runtime
- **Static Serving**: nginx serves built PWA assets from `/usr/share/nginx/html`
- **API Proxy**: Strips `/api` prefix and forwards to FMS server
- **Environment Config**: Runtime configuration via environment variables
- **Health Checks**: Built-in container health monitoring
- **Security**: Proper headers and HTTPS redirects

### Docker Files Structure
```
build/
├── Dockerfile              # Development container (Node.js + tools)
├── Dockerfile.prod         # Production multi-stage build
├── docker-compose.yml      # Development services (pwa-app + web + dns)
├── docker-compose.prod.yml # Production deployment
├── base.yml               # Shared service configuration
└── fonts/                 # Custom fonts for consistent rendering

.devbase/bin/
├── reboot-dev             # Development environment startup
├── build.prod             # Production build automation
└── cleanup-all-images     # Docker cleanup utility
```

### Optimizations & Best Practices
- **Consistent Package Management**: npm used throughout (no yarn conflicts)
- **Layer Caching**: Dependencies installed before source copy for faster rebuilds
- **Build Context**: Optimized `.dockerignore` excludes unnecessary files
- **Service Naming**: Consistent naming across all compose files and scripts
- **Network Configuration**: Proper bridge networking with external network support
- **Dependency Management**: Removed server-side tools from PWA development image
- **Sharp Workaround**: Automatic removal of problematic dependencies during build

## Key Components Deep Dive

### OfflineServiceManager
Main orchestrator component managing the three-step workflow:
- **Step 1**: Service initialization and metadata download
- **Step 2**: Form completion and local data management
- **Step 3**: Data submission and signature capture

### FMSSystemsRenderer
Handles system-specific operations:
- Dynamic system table rendering
- Form status tracking and completion
- Parts management and replacement tracking
- Document attachment handling

### SignaturePad
Digital signature capture with validation:
- Canvas-based signature drawing
- Print name requirement validation
- Local storage with encryption
- Upload preparation and submission

### Service Layer
Modular service architecture:
- **offlineServiceAPI**: FMS server communication
- **formDataService**: Local form data management
- **uploadService**: File and data upload coordination
- **indexedDBService**: Large file storage management

## Offline-First Strategy

### Data Storage Strategy
- **LocalStorage**: Encrypted service metadata and form data
- **IndexedDB**: Large files and attachments
- **Memory**: Temporary OTP and session data
- **Service Worker Cache**: Static assets and templates

### Sync Strategy
- **Queue-Based**: Operations queued when offline
- **Bulk Upload**: Efficient batch processing when online
- **Conflict Resolution**: Server data takes precedence
- **Progress Tracking**: Real-time status updates

### Cache Management
- **Template Caching**: Form templates cached on initialization
- **Asset Caching**: Static resources cached by service worker
- **Data Expiration**: Automatic cleanup after successful submission
- **Storage Limits**: 20MB cache limit with cleanup policies

---

**Note**: This PWA is designed to work in conjunction with the main FMS system and requires proper server configuration and OTP generation from supervisors for full functionality.
