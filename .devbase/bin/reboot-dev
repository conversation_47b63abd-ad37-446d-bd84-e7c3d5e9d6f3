#!/usr/bin/env bash
# FMS PWA Development Script - Following FMS patterns

# Find project root (works from any directory)
while [[ ! -f ".envrc" && "$PWD" != "/" ]]; do
  cd ..
done

if [[ ! -f ".envrc" ]]; then
  echo "Error: Could not find project root (.envrc not found)"
  exit 1
fi

# Source environment variables
source .envrc

# Check Docker Compose version
if [[ $(docker compose version) != *"v2"* ]]; then
  echo "docker compose version must be version 2"
  exit 1
fi

echo "🔄 Building and starting FMS PWA Docker environment..."

# Build and start services
docker compose build
docker compose kill pwa-app web 2>/dev/null || true
docker compose up -d --remove-orphans
docker compose restart web

echo "⏳ Waiting for services to start..."
sleep 8

echo "🧪 Testing services..."

# Test PWA accessibility
if curl -s http://localhost:${NGINX_PORT:-3000} > /dev/null; then
  echo "✅ PWA is accessible at http://localhost:${NGINX_PORT:-3000}"
else
  echo "❌ PWA is not accessible"
fi

# Test API proxy (expect JSON response, not 502 error)
API_RESPONSE=$(curl -s --max-time 10 "http://localhost:${NGINX_PORT:-3000}/api/offline-mode/metadata?serviceNumber=test&otp=test")
if echo "$API_RESPONSE" | grep -q "success\|error"; then
  echo "✅ API proxy is working"
else
  echo "❌ API proxy is not working (Response: '$API_RESPONSE')"
fi

echo ""
echo "🚀 FMS PWA Development Environment Started Successfully!"
echo "📱 PWA: http://localhost:${NGINX_PORT:-3000}"
echo "🔧 API Proxy: http://localhost:${NGINX_PORT:-3000}/api/*"
echo ""
