#!/usr/bin/env bash

set -e

# devbase: ensure=always
# devbase: executable=yes

if [ ! -z "$(git status --porcelain)" ]; then
  echo "Working directory is not clean, please revert or commit first"
  exit 1
fi

echo "git pull ..."
git pull

lastVersion=$(git describe | sed -e "s/-.*//")
echo "Last Version: ${lastVersion}"
nextMajorVersion=${lastVersion%.*}
nextMinorVersion=`expr ${lastVersion##*.} + 1`
proposedNextVersion="${nextMajorVersion}.${nextMinorVersion}"
read -p "New Version [${proposedNextVersion}] : " newVersion
echo "Confirmed New Version: ${newVersion:=$proposedNextVersion}"
git tag -a $newVersion -m "$newVersion"; git push origin --tags

echo "Building dev image for multi-stage"
docker compose build

echo "Building production image"
ASSETS_CONTEXT=$(cat /dev/urandom | tr -dc 'a-zA-Z0-9' | fold -w 6 | head -n 1)
set -x
docker build \
  --build-arg APP_VERSION=$newVersion \
  --build-arg ASSETS_CONTEXT=/assets-$ASSETS_CONTEXT \
  -t appcara.azurecr.io/fms-pwa:$newVersion -f build/Dockerfile.prod .
set +x

echo "Publish production image"
echo "docker push appcara.azurecr.io/fms-pwa:$newVersion"
docker push appcara.azurecr.io/fms-pwa:$newVersion

echo "Cleanup local image"
echo "docker rmi appcara.azurecr.io/fms-pwa:$newVersion"
docker rmi appcara.azurecr.io/fms-pwa:$newVersion
