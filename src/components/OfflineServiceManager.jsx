// Offline Service Manager Component for FMS PWA
// Handles OTP-based offline service workflow

import { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Card,
  CardContent,
  Grid,
  Alert,
  CircularProgress,
  LinearProgress,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Chip
} from '@mui/material';
import {
  VpnKey as VpnKeyIcon,
  Assignment as AssignmentIcon,
  Person as PersonIcon,
  CloudUpload as CloudUploadIcon,
  Refresh as RefreshIcon,
  PlayArrow as PlayArrowIcon,
  ArrowBack as ArrowBackIcon,
  Business as BusinessIcon,
  Engineering as EngineeringIcon,
  TaskAlt as TaskAltIcon,
  Sync as SyncIcon
} from '@mui/icons-material';
import SignaturePad from './SignaturePad';
import FMSSystemsRenderer from './FMSSystemsRenderer';
import UploadResultsDialog from './UploadResultsDialog';
import { useAppContext, useNetworkContext } from '../hooks/contextHooks';
// Direct service imports for better performance
import offlineServiceAPI from '../services/offlineServiceAPI';
import uploadService from '../services/uploadService';
import formDataService from '../services/formDataService';
import systemDataService from '../services/systemDataService';
import { useFormEngine } from '../hooks';

import { handleNetworkError, getUserFriendlyMessage, logError, createFormEnvironmentVariables, convertToTemplateId, localStorageManager } from '../utils';

// Shared styling constants
const STEP_STYLES = {
  card: { mt: 2, borderRadius: 2, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' },
  content: { p: 3 },
  title: { mb: 2, fontWeight: 600, color: 'primary.main' },
  description: { mb: 3, color: 'text.secondary' }
};



function OfflineServiceManager({ onError, onSuccess }) {
  // Use simplified context hooks for state management
  const { isOnline } = useNetworkContext();
  const appState = useAppContext(); // Combined service + queue state

  // Destructure combined app state (service + queue)
  const {
    currentStep,
    serviceNumber,
    otp,
    serviceMetadata,
    setCurrentStep,
    setServiceNumber,
    setOtp,
    setServiceMetadata,
    resetState,
    updateServiceMetadata
  } = appState;

  // Local state for dynamic statistics
  const [currentStatistics, setCurrentStatistics] = useState(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [pendingItems, setPendingItems] = useState({
    forms: 0,
    attachments: 0,
    replacedParts: 0,
    total: 0
  });

  // Upload results state for detailed feedback
  const [uploadResults, setUploadResults] = useState(null);

  // Utility to create failed items for retry
  const createFailedItems = (pendingItems) => {
    const failedResults = [];
    for (let i = 0; i < pendingItems.forms; i++) {
      failedResults.push({ form_id: `form_${i + 1}`, status: 'failed', retry_safe: true });
    }
    for (let i = 0; i < pendingItems.attachments; i++) {
      failedResults.push({ attachment_id: `attachment_${i + 1}`, status: 'failed', retry_safe: true });
    }
    for (let i = 0; i < pendingItems.replacedParts; i++) {
      failedResults.push({ record_id: `parts_${i + 1}`, status: 'failed', retry_safe: true });
    }
    return failedResults;
  };

  // Function to calculate pending items for submission
  const calculatePendingItems = async (metadata) => {
    if (!metadata) return { forms: 0, attachments: 0, replacedParts: 0, total: 0 };

    let pendingForms = 0;
    let totalAttachments = 0;
    let totalReplacedParts = 0;

    // Count pending forms (completed forms ready for upload)
    metadata.systems?.forEach(system => {
      if (system.forms) {
        system.forms.forEach(form => {
          // Check if form is completed/finalized
          if (form.status === 'Completed' || form.status === 'completed' || form.finalized === true) {
            pendingForms++;
          }
        });
      }
    });

    // Count attachments and replaced parts from systemDataService
    try {
      const allSystemData = systemDataService.getAllSystemDataForSync();

      // Count attachments across all systems
      Object.values(allSystemData.attachments).forEach(systemAttachments => {
        totalAttachments += systemAttachments.length;
      });

      // Count replaced parts across all systems
      Object.values(allSystemData.replacedParts).forEach(systemParts => {
        totalReplacedParts += systemParts.length;
      });
    } catch (error) {
      const standardizedError = handleNetworkError(error, 'calculate pending items');
      logError(standardizedError, 'OfflineServiceManager.calculatePendingItems');
    }

    const total = pendingForms + totalAttachments + totalReplacedParts;

    return {
      forms: pendingForms,
      attachments: totalAttachments,
      replacedParts: totalReplacedParts,
      total: total
    };
  };

  // Function to trigger refresh (called when system data changes)
  const triggerRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // Consolidated function to calculate and update all statistics
  const calculateAndUpdateStatistics = async (metadata = null) => {
    const targetMetadata = metadata || formDataService.getServiceMetadata() || serviceMetadata;

    if (!targetMetadata) return;

    // Calculate form statistics
    let totalForms = 0;
    let completedForms = 0;

    targetMetadata.systems?.forEach(system => {
      if (system.forms) {
        totalForms += system.forms.length;
        completedForms += system.forms.filter(form => form.status === 'Completed').length;
      }
    });

    const remainingForms = totalForms - completedForms;
    const completionPercentage = totalForms > 0 ? Math.round((completedForms / totalForms) * 100 * 100) / 100 : 0;

    const stats = {
      total_forms: totalForms,
      completed_forms: completedForms,
      remaining_forms: remainingForms,
      completion_percentage: completionPercentage
    };

    // Calculate pending items for submission
    const pending = await calculatePendingItems(targetMetadata);

    // Update state
    setCurrentStatistics(stats);
    setPendingItems(pending);

    // If we got fresh metadata from storage, update context
    if (metadata !== serviceMetadata) {
      setServiceMetadata(targetMetadata);
    }

    return { stats, pending };
  };

  // Function to refresh service data and statistics (called after form saves)
  const refreshServiceData = async () => {
    // Get fresh data from storage and update everything
    const freshMetadata = formDataService.getServiceMetadata();
    if (freshMetadata) {
      await calculateAndUpdateStatistics(freshMetadata);
      // Trigger re-render for child components
      setRefreshTrigger(prev => prev + 1);
    }
  };

  // Set up refresh callback for form saves (once)
  useEffect(() => {
    formDataService.setRefreshCallback(refreshServiceData);
    return () => formDataService.setRefreshCallback(null);
  }, []);

  // Handle serviceMetadata changes (consolidated)
  useEffect(() => {
    if (serviceMetadata) {
      calculateAndUpdateStatistics(serviceMetadata);
      setHasSignature(checkSignatureExists());
      calculatePendingItems(serviceMetadata).then(setPendingItems);
    }
  }, [serviceMetadata, refreshTrigger]);

  // Consolidated loading state management
  const [loadingStates, setLoadingStates] = useState({
    initialization: false,
    synchronization: false,
    upload: false,
    signature: false,
    retry: false
  });

  // Helper function to update loading states
  const setLoadingState = (operation, isLoading) => {
    setLoadingStates(prev => ({ ...prev, [operation]: isLoading }));
  };

  // Local state for UI
  const [selectedSystem, setSelectedSystem] = useState(null);
  const [showFormRenderer, setShowFormRenderer] = useState(false);
  const [showResetConfirmDialog, setShowResetConfirmDialog] = useState(false);
  const [showSyncConfirmDialog, setShowSyncConfirmDialog] = useState(false);
  const [hasSignature, setHasSignature] = useState(false);
  const [showUploadResultsDialog, setShowUploadResultsDialog] = useState(false);
  const [showSignatureResultDialog, setShowSignatureResultDialog] = useState(false);
  const [signatureUploadResult, setSignatureUploadResult] = useState(null);

  // Form engine hook with callbacks
  const formContainerRef = useRef(null);
  const formCloseTimeoutRef = useRef(null);

  // Define callbacks for form events
  const formCallbacks = {
    onFormClosed: () => {
      // Debounce form close events to prevent multiple rapid calls
      if (formCloseTimeoutRef.current) {
        clearTimeout(formCloseTimeoutRef.current);
      }

      formCloseTimeoutRef.current = setTimeout(() => {

        handleFormComplete();
        formCloseTimeoutRef.current = null;
      }, 100); // 100ms debounce
    },
    onFormSaved: () => {
      // Always allow form saving - save to local cache
    }
  };

  const { renderForm } = useFormEngine(formCallbacks);



  // Effect to render form when modal opens and selectedSystem changes
  useEffect(() => {
    if (showFormRenderer && selectedSystem && selectedSystem.templateId) {
      // Set up window.closeForm function that FormRenderingEngine will call (same as FormRenderer)
      window.closeForm = () => {

        handleFormComplete();
      };

      renderFormDirectly(selectedSystem.templateId, selectedSystem.environmentVariables, selectedSystem.formData);
    }
  }, [showFormRenderer, selectedSystem]);




  // Handle OTP-based service initialization
  const handleInitializeService = async () => {
    if (!serviceNumber.trim() || !otp.trim()) {
      onError?.('Please enter both Service Number and OTP');
      return;
    }

    if (!isOnline) {
      onError?.('Service initialization requires an internet connection');
      return;
    }

    setLoadingState('initialization', true);

    try {
      // First validate OTP format (6 digits)
      if (!/^\d{6}$/.test(otp.trim())) {
        throw new Error('OTP must be exactly 6 digits');
      }

      console.log('OfflineServiceManager: Fetching metadata for service:', serviceNumber.trim(), 'with OTP:', otp.trim());
      const metadata = await offlineServiceAPI.getServiceMetadata(serviceNumber.trim(), otp.trim());

      // Debug: Log the processed metadata
      console.log('OfflineServiceManager: Received metadata:', metadata);
      console.log('OfflineServiceManager: Systems array:', metadata?.systems);

      // Validate metadata structure
      if (!metadata || typeof metadata !== 'object') {
        console.error('OfflineServiceManager: Invalid metadata received:', metadata);
        throw new Error('Invalid service metadata received');
      }

      // Ensure systems array exists
      if (!metadata.systems) {
        console.log('OfflineServiceManager: No systems array found, initializing empty array');
        metadata.systems = [];
      } else {
        console.log('OfflineServiceManager: Found', metadata.systems.length, 'systems');
        metadata.systems.forEach((system, index) => {
          console.log(`OfflineServiceManager: System ${index + 1}:`, {
            id: system.id || system.system_id,
            name: system.name || system.system_name || system.fullname,
            type: system.system_type,
            forms: system.forms?.length || 0,
            assigned_user: system.assigned_user?.name
          });
        });
      }

      // Debug: Log final systems data
      console.log('OfflineServiceManager: Final systems data:', metadata.systems);

      // Use the service state hook to update metadata (this handles persistence automatically)
      updateServiceMetadata(metadata);

      // Initialize form data structure in service metadata for offline access
      const saveResult = formDataService.initializeServiceMetadata(metadata);
      if (!saveResult) {
        console.warn('OfflineServiceManager: Failed to initialize form data structure');
      } else {
        console.log('OfflineServiceManager: Form data structure initialized successfully');
      }

      console.log('Service initialized successfully:', metadata);
    } catch (err) {
      // Use standardized error handling
      const standardizedError = handleNetworkError(err, 'service initialization');
      logError(standardizedError, 'OfflineServiceManager.handleInitializeService');
      onError?.(getUserFriendlyMessage(standardizedError));
    } finally {
      setLoadingState('initialization', false);
    }
  };

  // Handle synchronization confirmation dialog
  const handleSyncClick = () => {
    setShowSyncConfirmDialog(true);
  };

  const handleCancelSync = () => {
    setShowSyncConfirmDialog(false);
  };

  // Handle confirmed service data synchronization (re-fetch metadata)
  const handleConfirmedSync = async () => {
    setShowSyncConfirmDialog(false);

    if (!serviceNumber.trim() || !otp.trim()) {
      onError?.('Service Number and OTP are required for synchronization');
      return;
    }

    if (!isOnline) {
      onError?.('Synchronization requires internet connection');
      return;
    }

    setLoadingState('synchronization', true);

    try {
      console.log('OfflineServiceManager: Synchronizing service data for:', serviceNumber.trim());

      // Re-fetch metadata using existing service number and OTP
      const metadata = await offlineServiceAPI.getServiceMetadata(serviceNumber.trim(), otp.trim());

      console.log('OfflineServiceManager: Received updated metadata:', metadata);

      // Process systems data same as initialization
      if (metadata.systems && Array.isArray(metadata.systems)) {
        metadata.systems = metadata.systems.map(system => ({
          ...system,
          forms: system.forms || []
        }));
      }

      // Update service metadata
      updateServiceMetadata(metadata);

      // Re-initialize form data structure with updated metadata
      const saveResult = formDataService.initializeServiceMetadata(metadata);
      if (!saveResult) {
        console.warn('OfflineServiceManager: Failed to re-initialize form data structure');
      } else {
        console.log('OfflineServiceManager: Form data structure re-initialized successfully');
      }

      // Recalculate statistics with updated metadata
      await calculateAndUpdateStatistics(metadata);

      onSuccess?.('Service data synchronized successfully. Local unsaved data has been replaced with server data.');
      console.log('Service data synchronized successfully:', metadata);
    } catch (err) {
      const standardizedError = handleNetworkError(err, 'service synchronization');
      logError(standardizedError, 'OfflineServiceManager.handleConfirmedSync');
      onError?.(getUserFriendlyMessage(standardizedError));
    } finally {
      setLoadingState('synchronization', false);
    }
  };

  // Note: Using centralized createFormEnvironmentVariables utility from utils/formEnvironment.js

  // Render form directly with environment variables using form engine hook
  const renderFormDirectly = async (templateId, envVars, formData = null) => {
    try {
      console.log('FMS Debug: Rendering form using hook:', templateId, 'with envVars:', envVars);

      // Prepare options with form data (including configurables from API)
      const options = {
        formData: formData // This includes configurables array from API
      };

      // Use the form engine hook to render the form
      await renderForm(templateId, envVars, options);

      console.log('FMS Debug: Form rendered successfully using hook');
    } catch (err) {
      // Use standardized error handling
      const standardizedError = handleNetworkError(err, 'form rendering');
      logError(standardizedError, 'OfflineServiceManager.renderFormDirectly');
      onError?.(getUserFriendlyMessage(standardizedError));
    }
  };

  // Handle system selection for form filling (from FMS frontend)
  const handleSystemFormSelect = (system, templateId = null, formData = null) => {
    // Use provided formData or find it by templateId
    const actualFormData = formData || (templateId ? system.forms?.find(f => f.template_id === templateId || f.code === templateId) : null);

    // Create environment variables using centralized utility
    const envVars = createFormEnvironmentVariables(system, actualFormData, serviceMetadata, serviceNumber, otp);

    // Convert templateId to template format using utility
    const convertedTemplateId = convertToTemplateId(templateId);

    // Store system context with form data for rendering
    setSelectedSystem({
      ...system,
      environmentVariables: envVars,
      formId: actualFormData?.id || templateId,
      templateId: convertedTemplateId,
      formData: actualFormData
    });

    // Open the modal - form will be rendered by useEffect when modal opens
    setShowFormRenderer(true);
  };



  // Handle form completion - FormRenderingEngine handles backdrop cleanup
  const handleFormComplete = () => {
    // Prevent multiple calls if form is already being closed
    if (!showFormRenderer) {
      return;
    }



    // Simply update React state - FormRenderingEngine handles all DOM cleanup
    setShowFormRenderer(false);
    setSelectedSystem(null);
  };

  // Function to check if signature and print name both exist (new format only)
  const checkSignatureExists = () => {
    const signatureKey = `fms-pwa-signature-${serviceMetadata?.service_data?.service_id || 'default'}`;
    const savedSignature = localStorageManager.getItem(signatureKey);

    if (!savedSignature) return false;

    // Both signature data and print name must be present
    return !!(
      savedSignature.dataURL &&
      savedSignature.printName &&
      savedSignature.printName.trim()
    );
  };

  // Handle signature saved to local storage
  const handleSignatureSaved = (signatureData) => {
    // Check if both signature and print name are present
    const hasCompleteSignature = !!(signatureData.dataURL && signatureData.printName && signatureData.printName.trim());
    setHasSignature(hasCompleteSignature);

    if (hasCompleteSignature) {
      onSuccess?.('Customer signature and print name saved locally. Ready to upload when online.');
    } else {
      onError?.('Both signature and print name are required for upload.');
    }
  };

  // Handle signature cleared/removed
  const handleSignatureCleared = () => {
    setHasSignature(false);
  };

  // Handle data upload (forms, attachments, replaced parts)
  const handleUploadData = async () => {
    if (!isOnline) {
      onError?.('Cannot upload data: Device is offline');
      return;
    }

    if (!serviceMetadata) {
      onError?.('No service data available for upload');
      return;
    }

    setLoadingState('upload', true);
    setUploadResults(null); // Clear previous results

    try {
      // Get service credentials from app state (not metadata)
      const otpValue = otp;

      if (!serviceNumber || !otpValue) {
        onError?.('Missing service credentials for upload');
        return;
      }



      // Upload all data (forms, attachments, parts records)
      const uploadResult = await uploadService.uploadAll(serviceNumber, otpValue, serviceMetadata);

      // Simple results structure
      const results = [
        ...(uploadResult.forms?.results || []),
        ...(uploadResult.attachments?.results || []),
        ...(uploadResult.replacedParts?.results || [])
      ];

      // If no results but have failures, create simple failed items
      if (results.length === 0 && uploadResult.overall?.failedItems > 0) {
        const pending = await calculatePendingItems(serviceMetadata);
        for (let i = 0; i < pending.forms; i++) {
          results.push({ form_id: `form_${i + 1}`, status: 'failed', retry_safe: true });
        }
        for (let i = 0; i < pending.attachments; i++) {
          results.push({ attachment_id: `attachment_${i + 1}`, status: 'failed', retry_safe: true });
        }
        for (let i = 0; i < pending.replacedParts; i++) {
          results.push({ record_id: `parts_${i + 1}`, status: 'failed', retry_safe: true });
        }
      }

      setUploadResults({
        totalItems: uploadResult.overall?.totalItems || 0,
        successfulItems: uploadResult.overall?.uploadedItems || 0,
        failedItems: uploadResult.overall?.failedItems || 0,
        successRate: uploadResult.overall?.success ? '100%' : '0%',
        results,
        error: uploadResult.overall?.success ? null : 'Upload failed'
      });

      // Update pending items after upload
      const updatedPending = await calculatePendingItems(serviceMetadata);
      setPendingItems(updatedPending);

      // Show upload results dialog
      setShowUploadResultsDialog(true);

    } catch (err) {
      const errorMessage = getUserFriendlyMessage(handleNetworkError(err, 'data upload'));

      setUploadResults({
        totalItems: pendingItems.total,
        successfulItems: 0,
        failedItems: pendingItems.total,
        successRate: '0%',
        results: createFailedItems(pendingItems),
        error: errorMessage
      });
      setShowUploadResultsDialog(true);
    } finally {
      setLoadingState('upload', false);
    }
  };



  // Simple retry logic
  const handleRetryAllFailed = async () => {
    if (!uploadResults?.results?.length) return;

    const failedItems = uploadResults.results.filter(r => r.status === 'failed');
    if (failedItems.length === 0) {
      setUploadResults(prev => ({ ...prev, retryMessage: 'No failed items to retry' }));
      return;
    }

    setLoadingState('retry', true);
    try {
      await handleUploadData();
      setUploadResults(prev => ({ ...prev, retryMessage: 'Retry completed' }));
    } catch (err) {
      setUploadResults(prev => ({ ...prev, retryMessage: 'Retry failed' }));
    } finally {
      setLoadingState('retry', false);
    }
  };

  // Handle signature upload only
  const handleUploadSignature = async () => {
    if (!isOnline) {
      onError?.('❌ Cannot upload signature: Device is offline');
      return;
    }

    if (!hasSignature) {
      onError?.('❌ Please provide Customer signature before uploading');
      return;
    }

    setLoadingState('signature', true);

    try {
      // Upload signature using uploadService

      const result = await uploadService.uploadSignature(
        serviceNumber,
        otp,
        serviceMetadata
      );

      // Handle successful upload with popup dialog
      if (result.success) {
        const completedAt = result.data?.completed_at ?
          new Date(result.data.completed_at).toLocaleString() :
          new Date().toLocaleString();

        setSignatureUploadResult({
          success: true,
          title: 'Signature Upload Successful',
          message: `${result.message} Service #${result.data?.service_number || serviceNumber} completed at ${completedAt}.`
        });

        // Update signature state (uploadService already cleared localStorage)
        setHasSignature(false);

        // Trigger a refresh of pending items
        setRefreshTrigger(prev => prev + 1);
      } else {
        // Handle case where result exists but success is false
        setSignatureUploadResult({
          success: false,
          title: 'Signature Upload Failed',
          message: result.message || 'Unknown error occurred'
        });
      }

      // Show the result dialog
      setShowSignatureResultDialog(true);

    } catch (err) {

      // Handle specific error types with popup dialog
      let errorMessage;
      if (err.context?.errorType === 'FORMS_INCOMPLETE') {
        const pendingCount = err.context.pendingFormsCount || 'some';
        errorMessage = `Cannot upload signature: ${pendingCount} forms are still pending. Complete all system forms first (Step 2).`;
      } else if (err.context?.errorType === 'OTP_INVALID') {
        errorMessage = `Invalid or expired OTP. Please request a new OTP from your supervisor.`;
      } else if (err.type === 'VALIDATION_ERROR') {
        errorMessage = `Signature validation failed: ${err.message}`;
      } else if (err.type === 'AUTHENTICATION_ERROR') {
        errorMessage = `Authentication failed: ${err.message}`;
      } else if (err.type === 'OFFLINE_ERROR') {
        errorMessage = `Device is offline. Connect to internet to upload signature. Your data is saved locally.`;
      } else {
        // Generic error handling
        const standardizedError = handleNetworkError(err, 'signature upload');
        logError(standardizedError, 'OfflineServiceManager.handleUploadSignature');
        errorMessage = getUserFriendlyMessage(standardizedError);
      }

      // Show error in popup dialog
      setSignatureUploadResult({
        success: false,
        title: 'Signature Upload Failed',
        message: errorMessage
      });
      setShowSignatureResultDialog(true);
    } finally {
      setLoadingState('signature', false);
    }
  };



  // Handle data submission (simplified - no queue processing)
  const handleSubmitData = async (e) => {
    e?.preventDefault();
    setUploadResults(null);

    try {
      await handleUploadData();
    } catch (err) {
      // Error handling is done in handleUploadData
      console.error('Data submission failed:', err);
    }
  };

  // Show reset confirmation dialog
  const handleResetClick = (event) => {
    // Blur the button to remove focus before opening dialog
    if (event.target) {
      event.target.blur();
    }
    setShowResetConfirmDialog(true);
  };



  // Handle confirmed reset
  const handleConfirmedReset = async () => {
    setShowResetConfirmDialog(false);

    try {
      await resetState();
      setSelectedSystem(null);
      setShowFormRenderer(false);
      onSuccess?.('All service data has been cleared, including attachments from IndexedDB. You can now initialize a new service.');
    } catch (error) {
      const standardizedError = handleNetworkError(error, 'reset application data');
      logError(standardizedError, 'OfflineServiceManager.handleResetService');
      onError?.(getUserFriendlyMessage(standardizedError));
    }
  };

  // Handle cancel reset
  const handleCancelReset = () => {
    setShowResetConfirmDialog(false);
  };

  // Helper function to get service info based on service type
  const getServiceInfo = (serviceMetadata) => {
    if (!serviceMetadata) return null;

    // PM services use order_info, others use ticket_info
    if (serviceMetadata.service_type === 'PM') {
      return serviceMetadata.order_info;
    }
    return serviceMetadata.ticket_info;
  };

  // Helper function to format date/time
  const formatDateTime = (dateTimeString) => {
    if (!dateTimeString) return 'N/A';
    try {
      return new Date(dateTimeString).toLocaleString();
    } catch (e) {
      return dateTimeString;
    }
  };

  // Helper function to format date only
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch (e) {
      return dateString;
    }
  };

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 2 }}>
      {/* Modern Header */}
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Typography
          variant="h3"
          component="h1"
          sx={{
            fontWeight: 700,
            background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            mb: 1
          }}
        >
          FMS Offline Service
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 3 }}>
          Complete your field service tasks offline
        </Typography>


      </Box>

      {/* Modern Workflow Stepper */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          borderRadius: 3,
          border: '1px solid',
          borderColor: 'divider',
          background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
        }}
      >
        <Stepper
          activeStep={currentStep}
          orientation="vertical"
          sx={{
            '& .MuiStepLabel-root': {
              padding: '8px 0',
            },
            '& .MuiStepLabel-label': {
              fontSize: '1.1rem',
              fontWeight: 600,
              '&.Mui-active': {
                color: 'primary.main',
                fontWeight: 700,
              },
              '&.Mui-completed': {
                color: 'success.main',
                fontWeight: 600,
              }
            },
            '& .MuiStepIcon-root': {
              fontSize: '1.5rem',
              '&.Mui-active': {
                color: 'primary.main',
              },
              '&.Mui-completed': {
                color: 'success.main',
              }
            },
            '& .MuiStepContent-root': {
              borderLeft: '2px solid',
              borderColor: 'divider',
              marginLeft: '12px',
              paddingLeft: '24px',
            }
          }}
        >
          {/* Step 1: Initialize Service */}
          <Step>
            <StepLabel>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <VpnKeyIcon sx={{ fontSize: '1.2rem' }} />
                Initialize Service with OTP
              </Box>
            </StepLabel>
            <StepContent>
              <Card sx={STEP_STYLES.card}>
                <CardContent sx={STEP_STYLES.content}>
                  <Typography variant="body1" sx={STEP_STYLES.description}>
                    Enter the Service Number and OTP provided by your supervisor to download service data for offline processing.
                  </Typography>

                  <Grid container spacing={3} sx={{ mb: 3 }}>
                    <Grid size={{ xs: 12, sm: 6 }}>
                      <TextField
                        label="Service Number"
                        value={serviceNumber}
                        onChange={(e) => setServiceNumber(e.target.value)}
                        fullWidth
                        placeholder="e.g., PM25-DEV-A0006"
                        variant="outlined"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 2,
                          }
                        }}
                      />
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6 }}>
                      <TextField
                        label="OTP"
                        value={otp}
                        onChange={(e) => setOtp(e.target.value)}
                        fullWidth
                        placeholder="Enter 6-digit OTP"
                        slotProps={{
                          htmlInput: { maxLength: 6 }
                        }}
                        variant="outlined"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 2,
                          }
                        }}
                      />
                    </Grid>
                  </Grid>

                  <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                    <Button
                      variant="contained"
                      size="large"
                      startIcon={loadingStates.initialization ? <CircularProgress size={20} color="inherit" /> : <PlayArrowIcon />}
                      onClick={handleInitializeService}
                      disabled={loadingStates.initialization || !serviceNumber.trim() || !otp.trim()}
                      sx={{
                        borderRadius: 2,
                        px: 4,
                        py: 1.5,
                        background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                        boxShadow: '0 4px 20px rgba(33, 150, 243, 0.3)',
                        '&:hover': {
                          boxShadow: '0 6px 25px rgba(33, 150, 243, 0.4)',
                        },
                        '&:disabled': {
                          background: 'grey.300',
                          boxShadow: 'none',
                        }
                      }}
                    >
                      {loadingStates.initialization ? 'Initializing Service...' : 'Initialize Service'}
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </StepContent>
          </Step>

          {/* Step 2: Complete System Forms */}
          <Step>
            <StepLabel>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <AssignmentIcon sx={{ fontSize: '1.2rem' }} />
                Complete System Forms
              </Box>
            </StepLabel>
            <StepContent>
              {serviceMetadata && (
                <Box>
                  {/* Modern Service Information Cards */}
                  <Grid container spacing={3} sx={{ mb: 4 }}>
                    {/* Main Service Card */}
                    <Grid size={12}>
                      <Card
                        sx={{
                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                          color: 'white',
                          borderRadius: 3,
                          boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
                        }}
                      >
                        <CardContent sx={{ p: 3 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <EngineeringIcon sx={{ fontSize: 32, mr: 2 }} />
                            <Box>
                              <Typography variant="h5" sx={{ fontWeight: 600, mb: 0.5 }}>
                                {serviceMetadata.service_number || serviceNumber}
                              </Typography>
                              <Typography variant="body1" sx={{ opacity: 0.9 }}>
                                {serviceMetadata.service_type || 'Service'} • {getServiceInfo(serviceMetadata)?.service_date || 'N/A'}
                              </Typography>
                            </Box>
                            <Box sx={{ ml: 'auto', textAlign: 'right' }}>
                              <Chip
                                label={getServiceInfo(serviceMetadata)?.status || 'N/A'}
                                clickable={false}
                                onClick={() => {}} // No-op function to prevent onClick errors
                                sx={{
                                  backgroundColor: 'rgba(255,255,255,0.2)',
                                  color: 'white',
                                  fontWeight: 600,
                                  mb: 1,
                                  cursor: 'default',
                                  pointerEvents: 'none' // Completely disable clicking
                                }}
                              />
                              <Typography variant="body2" sx={{ opacity: 0.8 }}>
                                {getServiceInfo(serviceMetadata)?.completion_status || 'N/A'}
                              </Typography>
                            </Box>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>

                    {/* Job Information */}
                    <Grid size={{ xs: 12, md: 6 }}>
                      <Card sx={{ height: '100%', borderRadius: 2, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                        <CardContent sx={{ p: 3 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <BusinessIcon sx={{ color: 'primary.main', mr: 1.5 }} />
                            <Typography variant="h6" sx={{ fontWeight: 600 }}>
                              Job Information
                            </Typography>
                          </Box>
                          <Box sx={{ space: 2 }}>
                            <Typography variant="body1" sx={{ mb: 1 }}>
                              <strong>Job No:</strong> {serviceMetadata.job_info?.job_no || 'N/A'}
                            </Typography>
                            <Typography variant="body1" sx={{ mb: 1 }}>
                              <strong>End User:</strong> {serviceMetadata.job_info?.end_user || 'N/A'}
                            </Typography>
                            <Typography variant="body1" sx={{ mb: 1 }}>
                              <strong>Contact:</strong> {serviceMetadata.job_info?.contact || 'N/A'}
                            </Typography>
                            <Typography variant="body1" sx={{ mb: 1 }}>
                              <strong>Contact Number:</strong> {serviceMetadata.job_info?.contact_number || 'N/A'}
                            </Typography>
                            <Typography variant="body1">
                              <strong>Service Address:</strong> {serviceMetadata.job_info?.service_address || 'N/A'}
                            </Typography>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>

                    {/* Progress Information */}
                    <Grid size={{ xs: 12, md: 6 }}>
                      <Card sx={{ height: '100%', borderRadius: 2, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                        <CardContent sx={{ p: 3 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <TaskAltIcon sx={{ color: 'success.main', mr: 1.5 }} />
                            <Typography variant="h6" sx={{ fontWeight: 600 }}>
                              Progress
                            </Typography>
                          </Box>
                          <Box sx={{ space: 2 }}>
                            <Typography variant="body1" sx={{ mb: 1 }}>
                              <strong>Total Forms:</strong> {currentStatistics?.total_forms || 0}
                            </Typography>
                            <Typography variant="body1" sx={{ mb: 1 }}>
                              <strong>Completed:</strong> {currentStatistics?.completed_forms || 0}
                            </Typography>
                            <Typography variant="body1" sx={{ mb: 2 }}>
                              <strong>Remaining:</strong> {currentStatistics?.remaining_forms || 0}
                            </Typography>
                            <LinearProgress
                              variant="determinate"
                              value={currentStatistics?.completion_percentage || 0}
                              sx={{
                                height: 8,
                                borderRadius: 4,
                                backgroundColor: 'grey.200',
                                '& .MuiLinearProgress-bar': {
                                  borderRadius: 4,
                                  backgroundColor: 'success.main'
                                }
                              }}
                            />
                            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                              {currentStatistics?.completion_percentage || 0}% Complete
                            </Typography>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>

                    {/* Service Details */}
                    <Grid size={12}>
                      <Card sx={{ borderRadius: 2, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                        <CardContent sx={{ p: 3 }}>
                          <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                            Service Details
                          </Typography>
                          <Grid container spacing={2}>
                            {/* Service-specific fields based on service type */}
                            {serviceMetadata.service_type === 'EC' && serviceMetadata.ticket_info && (
                              <>
                                <Grid size={{ xs: 12, sm: 6 }}>
                                  <Typography variant="body1" sx={{ mb: 1 }}>
                                    <strong>Received Call:</strong> {formatDateTime(serviceMetadata.ticket_info.received_call)}
                                  </Typography>
                                </Grid>
                                <Grid size={{ xs: 12, sm: 6 }}>
                                  <Typography variant="body1" sx={{ mb: 1 }}>
                                    <strong>Problem:</strong> {serviceMetadata.ticket_info.problem || 'N/A'}
                                  </Typography>
                                </Grid>
                                <Grid size={{ xs: 12, sm: 6 }}>
                                  <Typography variant="body1" sx={{ mb: 1 }}>
                                    <strong>Action Taken:</strong> {serviceMetadata.ticket_info.action_taken || 'N/A'}
                                  </Typography>
                                </Grid>
                                <Grid size={{ xs: 12, sm: 6 }}>
                                  <Typography variant="body1" sx={{ mb: 1 }}>
                                    <strong>External Ref No:</strong> {serviceMetadata.ticket_info.external_ref_no || 'N/A'}
                                  </Typography>
                                </Grid>
                              </>
                            )}

                            {serviceMetadata.service_type === 'SV' && serviceMetadata.ticket_info && (
                              <>
                                <Grid size={{ xs: 12, sm: 6 }}>
                                  <Typography variant="body1" sx={{ mb: 1 }}>
                                    <strong>Service Date:</strong> {formatDate(serviceMetadata.ticket_info.service_date)}
                                  </Typography>
                                </Grid>
                                <Grid size={{ xs: 12, sm: 6 }}>
                                  <Typography variant="body1" sx={{ mb: 1 }}>
                                    <strong>Requested Service:</strong> {serviceMetadata.ticket_info.requested_service || 'N/A'}
                                  </Typography>
                                </Grid>
                                <Grid size={{ xs: 12, sm: 6 }}>
                                  <Typography variant="body1" sx={{ mb: 1 }}>
                                    <strong>Leave Time:</strong> {formatDateTime(serviceMetadata.ticket_info.leave_time)}
                                  </Typography>
                                </Grid>
                                <Grid size={{ xs: 12, sm: 6 }}>
                                  <Typography variant="body1" sx={{ mb: 1 }}>
                                    <strong>Signed Off:</strong> {serviceMetadata.ticket_info.signed_off || 'N/A'}
                                  </Typography>
                                </Grid>
                                <Grid size={{ xs: 12, sm: 6 }}>
                                  <Typography variant="body1" sx={{ mb: 1 }}>
                                    <strong>Action Taken:</strong> {serviceMetadata.ticket_info.action_taken || 'N/A'}
                                  </Typography>
                                </Grid>
                                <Grid size={{ xs: 12, sm: 6 }}>
                                  <Typography variant="body1" sx={{ mb: 1 }}>
                                    <strong>Internal Ref No:</strong> {serviceMetadata.ticket_info.internal_ref_no || 'N/A'}
                                  </Typography>
                                </Grid>
                                <Grid size={{ xs: 12, sm: 6 }}>
                                  <Typography variant="body1" sx={{ mb: 1 }}>
                                    <strong>Quotation PO No:</strong> {serviceMetadata.ticket_info.quotation_po_no || 'N/A'}
                                  </Typography>
                                </Grid>
                              </>
                            )}

                            {serviceMetadata.service_type === 'PM' && serviceMetadata.order_info && (
                              <>
                                <Grid size={{ xs: 12, sm: 6 }}>
                                  <Typography variant="body1" sx={{ mb: 1 }}>
                                    <strong>Service Date:</strong> {formatDate(serviceMetadata.order_info.service_date)}
                                  </Typography>
                                </Grid>
                                <Grid size={{ xs: 12, sm: 6 }}>
                                  <Typography variant="body1" sx={{ mb: 1 }}>
                                    <strong>Team:</strong> {serviceMetadata.order_info.team || 'N/A'}
                                  </Typography>
                                </Grid>
                              </>
                            )}

                            {/* Common fields */}
                            <Grid size={{ xs: 12, sm: 6 }}>
                              <Typography variant="body1" sx={{ mb: 1 }}>
                                <strong>Assignees:</strong>
                              </Typography>
                              <Typography variant="body1" sx={{ ml: 2, whiteSpace: 'pre-line' }}>
                                {getServiceInfo(serviceMetadata)?.assignees || 'N/A'}
                              </Typography>
                            </Grid>
                            <Grid size={{ xs: 12, sm: 6 }}>
                              <Typography variant="body1" sx={{ mb: 1 }}>
                                <strong>Arrival Time:</strong> {formatDateTime(getServiceInfo(serviceMetadata)?.arrival_time)}
                              </Typography>
                            </Grid>

                            {/* Remarks */}
                            {getServiceInfo(serviceMetadata)?.remarks && getServiceInfo(serviceMetadata).remarks !== '-' && (
                              <Grid size={12}>
                                <Typography variant="body1" sx={{ mt: 2 }}>
                                  <strong>Remarks:</strong> {getServiceInfo(serviceMetadata).remarks}
                                </Typography>
                              </Grid>
                            )}
                          </Grid>
                        </CardContent>
                      </Card>
                    </Grid>
                  </Grid>

                  {/* Systems List */}
                  <FMSSystemsRenderer
                    serviceMetadata={serviceMetadata}
                    onSystemFormSelect={handleSystemFormSelect}
                    refreshTrigger={refreshTrigger}
                    onDataChange={triggerRefresh}
                  />

                  {/* Action Buttons */}
                  <Box sx={{ mt: 4, display: 'flex', gap: 2, justifyContent: 'center' }}>
                    <Button
                      variant="outlined"
                      onClick={handleResetClick}
                      startIcon={<RefreshIcon />}
                      sx={{
                        borderRadius: 2,
                        px: 3,
                        py: 1.5,
                        borderColor: 'grey.400',
                        color: 'grey.700',
                        '&:hover': {
                          borderColor: 'grey.600',
                          backgroundColor: 'grey.50'
                        }
                      }}
                    >
                      Reset Service
                    </Button>
                    <Button
                      variant="contained"
                      onClick={() => setCurrentStep(2)}
                      disabled={!serviceMetadata.systems || serviceMetadata.systems.length === 0}
                      startIcon={<PlayArrowIcon />}
                      sx={{
                        borderRadius: 2,
                        px: 4,
                        py: 1.5,
                        background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                        boxShadow: '0 4px 20px rgba(33, 150, 243, 0.3)',
                        '&:hover': {
                          boxShadow: '0 6px 25px rgba(33, 150, 243, 0.4)',
                        }
                      }}
                    >
                      Continue to Submit
                    </Button>
                  </Box>
                </Box>
              )}
            </StepContent>
          </Step>

          {/* Step 3: Submit & Signoff */}
          <Step>
            <StepLabel>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PersonIcon sx={{ fontSize: '1.2rem' }} />
                Submit & Signoff
              </Box>
            </StepLabel>
            <StepContent>
              <Card sx={STEP_STYLES.card}>
                <CardContent sx={STEP_STYLES.content}>
                  <Typography variant="h6" sx={STEP_STYLES.title}>
                    Submit Data & Customer Signature
                  </Typography>

                  <Typography variant="body1" sx={STEP_STYLES.description}>
                    Review pending items and submit data when online. Customer signature required to complete service.
                  </Typography>

                  {/* Pending Items Summary */}
                  <Card sx={{ mb: 3, backgroundColor: 'grey.50', borderRadius: 2 }}>
                    <CardContent sx={{ p: 2 }}>
                      <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'text.primary' }}>
                        Pending Items for Upload
                      </Typography>

                      <Grid container spacing={2}>
                        <Grid size={{ xs: 12, sm: 4 }}>
                          <Box sx={{ textAlign: 'center', p: 2, backgroundColor: 'white', borderRadius: 1 }}>
                            <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                              {pendingItems.forms}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Forms Completed
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid size={{ xs: 12, sm: 4 }}>
                          <Box sx={{ textAlign: 'center', p: 2, backgroundColor: 'white', borderRadius: 1 }}>
                            <Typography variant="h4" sx={{ fontWeight: 700, color: 'info.main' }}>
                              {pendingItems.attachments}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Attachments
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid size={{ xs: 12, sm: 4 }}>
                          <Box sx={{ textAlign: 'center', p: 2, backgroundColor: 'white', borderRadius: 1 }}>
                            <Typography variant="h4" sx={{ fontWeight: 700, color: 'warning.main' }}>
                              {pendingItems.replacedParts}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Replaced Parts
                            </Typography>
                          </Box>
                        </Grid>
                      </Grid>

                      <Box sx={{ mt: 2, textAlign: 'center' }}>
                        <Typography variant="h5" sx={{ fontWeight: 600, color: 'text.primary' }}>
                          Total: {pendingItems.total} Items Pending Upload
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>

                  {/* Online Status Alert */}
                  {!isOnline && (
                    <Alert severity="warning" sx={{ mb: 3, borderRadius: 2 }}>
                      <strong>Device is offline.</strong> Data submission requires an internet connection.
                      Items will be queued for upload when connection is restored.
                    </Alert>
                  )}

                  {isOnline && pendingItems.total > 0 && (
                    <Alert severity="info" sx={{ mb: 3, borderRadius: 2 }}>
                      <strong>Ready to submit.</strong> {pendingItems.total} items are ready for upload to the server.
                    </Alert>
                  )}

                  {/* Data Submission and Synchronization Buttons */}
                  <Box sx={{ mb: 3, display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                    <Button
                      variant="contained"
                      onClick={handleSubmitData}
                      disabled={!isOnline || pendingItems.total === 0 || loadingStates.upload}
                      startIcon={loadingStates.upload ? <CircularProgress size={20} color="inherit" /> : <CloudUploadIcon />}
                      sx={{
                        borderRadius: 2,
                        px: 4,
                        py: 1.5,
                        background: isOnline ? 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)' : 'grey.300',
                        boxShadow: isOnline ? '0 4px 20px rgba(33, 150, 243, 0.3)' : 'none',
                        '&:hover': {
                          boxShadow: isOnline ? '0 6px 25px rgba(33, 150, 243, 0.4)' : 'none',
                        },
                        '&:disabled': {
                          background: 'grey.300',
                          boxShadow: 'none',
                        }
                      }}
                    >
                      {loadingStates.upload ? 'Submitting Data...' : `Submit ${pendingItems.total} Items`}
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={handleSyncClick}
                      disabled={!isOnline || loadingStates.synchronization || !serviceNumber.trim() || !otp.trim()}
                      startIcon={loadingStates.synchronization ? <CircularProgress size={20} color="inherit" /> : <SyncIcon />}
                      sx={{
                        borderRadius: 2,
                        px: 3,
                        py: 1.5,
                        borderColor: isOnline ? 'warning.main' : 'grey.400',
                        color: isOnline ? 'warning.main' : 'grey.700',
                        '&:hover': {
                          borderColor: isOnline ? 'warning.dark' : 'grey.600',
                          backgroundColor: isOnline ? 'warning.50' : 'grey.50'
                        },
                        '&:disabled': {
                          borderColor: 'grey.300',
                          color: 'grey.500'
                        }
                      }}
                    >
                      {loadingStates.synchronization ? 'Synchronizing...' : 'Synchronize Service Data'}
                    </Button>
                  </Box>



                  {/* Signature Section */}
                  <Box sx={{ borderTop: '1px solid', borderColor: 'divider', pt: 3 }}>
                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: 'text.primary' }}>
                      Customer Signature
                    </Typography>

                    <Typography variant="body2" sx={{ mb: 2, color: 'text.secondary' }}>
                      The supervisor holding the device should obtain customer signature to complete the service.
                    </Typography>

                    {/* Signature Status Alerts */}
                    {!isOnline && (
                      <Alert severity="warning" sx={{ mb: 2, borderRadius: 2 }}>
                        <strong>Device is offline.</strong> Signature upload requires an internet connection.
                      </Alert>
                    )}

                    {isOnline && !hasSignature && (
                      <Alert severity="info" sx={{ mb: 2, borderRadius: 2 }}>
                        <strong>Signature & Print Name required.</strong> Please complete both customer signature and print name below to enable upload.
                      </Alert>
                    )}

                    {isOnline && hasSignature && (
                      <Alert severity="success" sx={{ mb: 2, borderRadius: 2 }}>
                        <strong>Ready to upload.</strong> Customer signature and print name are saved and ready for submission.
                      </Alert>
                    )}

                    {/* Signature Pad Component */}
                    <Box sx={{ mb: 3 }}>
                      <SignaturePad
                        storageKey={`fms-pwa-signature-${serviceMetadata?.service_data?.service_id || 'default'}`}
                        title="Customer Signature"
                        height={200}
                        onSignatureSaved={handleSignatureSaved}
                        onSignatureCleared={handleSignatureCleared}
                      />
                    </Box>
                  </Box>

                  {/* Action Buttons */}
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
                    <Button
                      variant="outlined"
                      onClick={() => setCurrentStep(1)}
                      startIcon={<ArrowBackIcon />}
                      sx={{
                        borderRadius: 2,
                        px: 3,
                        py: 1.5,
                        borderColor: 'grey.400',
                        color: 'grey.700',
                        '&:hover': {
                          borderColor: 'grey.600',
                          backgroundColor: 'grey.50'
                        }
                      }}
                    >
                      Back to Forms
                    </Button>
                    <Button
                      variant="contained"
                      onClick={handleUploadSignature}
                      disabled={!serviceMetadata?.systems || serviceMetadata.systems.length === 0 || !isOnline || !hasSignature || loadingStates.signature}
                      startIcon={loadingStates.signature ? <CircularProgress size={20} color="inherit" /> : <PersonIcon />}
                      sx={{
                        borderRadius: 2,
                        px: 4,
                        py: 1.5,
                        background: (isOnline && hasSignature) ? 'linear-gradient(45deg, #4CAF50 30%, #8BC34A 90%)' : 'grey.300',
                        boxShadow: (isOnline && hasSignature) ? '0 4px 20px rgba(76, 175, 80, 0.3)' : 'none',
                        '&:hover': {
                          boxShadow: (isOnline && hasSignature) ? '0 6px 25px rgba(76, 175, 80, 0.4)' : 'none',
                        },
                        '&:disabled': {
                          background: 'grey.300',
                          boxShadow: 'none',
                        }
                      }}
                    >
                      {loadingStates.signature ? 'Uploading Signature & Print Name...' : 'Upload Customer Signature & Print Name'}
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </StepContent>
          </Step>
        </Stepper>
      </Paper>
      {/* Hidden Form Container - FormRenderingEngine creates backdrop directly in body */}
      {showFormRenderer && (
        <div
          ref={formContainerRef}
          className="form-container"
          id="fms-form-container"
          style={{
            display: 'none', // Hidden - FormRenderingEngine renders popup directly to body
            fontFamily: 'DejaVu Sans, system-ui, sans-serif'
          }}
        />
      )}
      {/* Reset Confirmation Dialog */}
      <Dialog
        open={showResetConfirmDialog}
        onClose={handleCancelReset}
        aria-labelledby="reset-dialog-title"
        aria-describedby="reset-dialog-description"
        maxWidth="sm"
        fullWidth
        disableRestoreFocus
        disableEnforceFocus={false}
      >
        <DialogTitle id="reset-dialog-title" sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <RefreshIcon sx={{ mr: 1, color: 'warning.main' }} />
            Reset Service Data
          </Box>
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="reset-dialog-description" sx={{ mb: 2 }}>
            Are you sure you want to reset all service data? This action will completely clear:
          </DialogContentText>
          <Box component="ul" sx={{ pl: 2, mb: 2, color: 'text.secondary' }}>
            <li>All cached service metadata and system information</li>
            <li>All saved form data (both API data and user entries)</li>
            <li>All queued form and signature submissions</li>
            <li>All downloaded form templates and assets</li>
            <li>All FMS PWA related local storage data</li>
            <li>All attachments and files stored in IndexedDB</li>
            <li>Application state and progress</li>
          </Box>
          <Alert severity="warning" sx={{ mt: 2 }}>
            <strong>Warning:</strong> This action cannot be undone. All local data will be permanently deleted and you will need to re-initialize the service.
          </Alert>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={handleCancelReset}
            variant="outlined"
            sx={{ mr: 1 }}
            autoFocus
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirmedReset}
            variant="contained"
            color="warning"
            startIcon={<RefreshIcon />}
          >
            Reset Service
          </Button>
        </DialogActions>
      </Dialog>

      {/* Upload Results Dialog */}
      <UploadResultsDialog
        open={showUploadResultsDialog}
        onClose={() => setShowUploadResultsDialog(false)}
        uploadResults={uploadResults}
        onRetryFailed={handleRetryAllFailed}
        loading={loadingStates.upload || loadingStates.retry}
      />

      {/* Signature Upload Result Dialog */}
      <Dialog
        open={showSignatureResultDialog}
        onClose={() => setShowSignatureResultDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          color: signatureUploadResult?.success ? 'success.main' : 'error.main'
        }}>
          {signatureUploadResult?.success ? '✅' : '❌'} {signatureUploadResult?.title}
        </DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ fontSize: '1rem', lineHeight: 1.6 }}>
            {signatureUploadResult?.message}
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button
            onClick={() => setShowSignatureResultDialog(false)}
            variant="contained"
            color={signatureUploadResult?.success ? 'success' : 'primary'}
            sx={{ borderRadius: 2, px: 4 }}
          >
            {signatureUploadResult?.success ? 'Great!' : 'OK'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Synchronization Confirmation Dialog */}
      <Dialog
        open={showSyncConfirmDialog}
        onClose={handleCancelSync}
        aria-labelledby="sync-dialog-title"
        aria-describedby="sync-dialog-description"
        maxWidth="sm"
        fullWidth
        disableRestoreFocus
        disableEnforceFocus={false}
      >
        <DialogTitle id="sync-dialog-title" sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SyncIcon sx={{ mr: 1, color: 'warning.main' }} />
            Synchronize Service Data
          </Box>
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="sync-dialog-description" sx={{ mb: 2 }}>
            <strong>Important:</strong> Before synchronizing, please submit any unsaved local data to the server first.
          </DialogContentText>
          <DialogContentText sx={{ mb: 2 }}>
            Synchronization will replace your local service data with the latest data from the server. This includes:
          </DialogContentText>
          <Box component="ul" sx={{ pl: 2, mb: 2, color: 'text.secondary' }}>
            <li>Service metadata and system information</li>
            <li>Form templates and configurations</li>
            <li>System status and requirements</li>
          </Box>
          <Alert severity="warning" sx={{ mt: 2 }}>
            <strong>Warning:</strong> Any unsaved local form data, attachments, or changes will be lost and replaced with server data. Make sure to submit all pending items first.
          </Alert>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={handleCancelSync}
            variant="outlined"
            sx={{ mr: 1 }}
            autoFocus
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirmedSync}
            variant="contained"
            color="warning"
            startIcon={<SyncIcon />}
          >
            Synchronize Now
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default OfflineServiceManager;
