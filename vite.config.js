import { VitePWA } from 'vite-plugin-pwa';
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), VitePWA({
    // Let VitePWA handle registration automatically
    registerType: 'autoUpdate',
    injectRegister: 'auto',

    // Enable PWA in development for testing
    devOptions: {
      enabled: true,
      type: 'module',
      navigateFallback: 'index.html',
    },

    // Disable automatic PWA assets generation for now - use manual icons
    pwaAssets: {
      disabled: true,
    },

    // Manual manifest with proper icon configuration
    manifest: {
      name: 'FMS PWA - Facility Management System',
      short_name: 'FMS PWA',
      description: 'Offline-capable Progressive Web App for Facility Management System',
      theme_color: '#1976d2',
      background_color: '#ffffff',
      display: 'standalone',
      scope: '/',
      start_url: '/',
      icons: [
        {
          src: '/assets/static/img/logo.png',
          sizes: '512x512',
          type: 'image/png',
          purpose: 'any'
        },
        {
          src: '/assets/static/img/logo.png',
          sizes: '192x192',
          type: 'image/png',
          purpose: 'maskable'
        },
        {
          src: '/assets/static/img/favicon-16x16.png',
          sizes: '16x16',
          type: 'image/png',
          purpose: 'any'
        }
      ]
    },

    workbox: {
      cleanupOutdatedCaches: true,
      clientsClaim: true,
      skipWaiting: true,
      maximumFileSizeToCacheInBytes: 20 * 1024 * 1024, // 20MB limit for large assets

      // Precache all static assets including FMS assets
      globPatterns: [
        '**/*.{js,css,html,ico,png,jpg,svg,json,woff,woff2,ttf,eot}'
      ],

      // Exclude background images from service worker cache (keep icons)
      globIgnores: [
        '**/assets/static/img/bg*.jpg'
      ]
    }
  })],

  build: {
    chunkSizeWarningLimit: 3000 // Increase limit to 3MB for single bundle PWA strategy
  },

  server: {
    host: '0.0.0.0',
    port: 5173,
    allowedHosts: ['fmspwa-dev.com', 'localhost', '127.0.0.1'],
    proxy: {
      '/api': {
        target: 'https://fms-dev.com/',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
  },
})